import asyncio
import base64
import functools
import io
import json
import logging
import mimetypes
import os
import shutil
import time
from typing import Union, Any, Literal

import numpy as np
import pdf2image
import pymupdf
from PIL import Image
from PIL import ImageOps
from fastapi import UploadFile, HTTPException
from langchain.chat_models.base import BaseChatModel
from langchain_core.documents import Document
from langchain_core.messages import HumanMessage, SystemMessage
from typing_extensions import AsyncGenerator

from ira_chat.config.shared_config import SharedConfig
from ira_chat.db import api as db_api
from ira_chat.db import models
from ira_chat.services import tesseract, file_formats
from ira_chat.services.detections import posology, barcode
from ira_chat.services.detections import smart_merge
from ira_chat.services.file_processor import get_model_context_limit, count_tokens, split_document
from ira_chat.services.llms.base import write_metrics
from ira_chat.utils import llm_utils, retry_utils
from ira_chat.utils import metric_utils
from ira_chat.utils.http_utils import download_content
from ira_chat.utils.json_utils import extract_json

SMALL_SLEEP = 0.01
logger = logging.getLogger(__name__)
_prescription_prompt_template = """
Extract all fields from the image.
---
- The output should be in json format enclosed within ```
- The output should have these json fields:

"description": "",
"patient": "",
"doctor": "",
"prescription": [{{"medicament": "", "dosage": ""}}],
"date": ""
"""

DOCUMENT_PROMPT = """
Perform the following tasks on the provided image:
1. Provide a detailed description of the image.
2. Extract all text from the image and output it as clean plain text.

Ensure the output does not contain repeated punctuation or extra whitespace.
"""

DEFAULT_SYSTEM_PROMPT = """
Extract all fields manually. 
- Detect and recognize the handwritten medicament if present and write in the unknown field.
- If there is a double line square with a number inside present at the bottom of the document, recognize the number.
- The output should be in json format.
- The output should have these json fields:
- everything other field should be placed in separate "unknown" section 
- if prescription is divided into sections labeled "AFFECTION EXONERANTE" and "MALADIES INTERCURRENTES" put the section name in the "BiZone" field
- if there is any additional text generated after the json formatting put it in the extra field
- Do not output anything outside of JSON

"description": "",
"medic": {"RPPS": "", "AM": "", "Nom": "", "Prenom": "","Tel": "","Title": "","Speciality": "","Address": "",},
"date": "",
"patient": {"NomPrenom": "","Sex": "","Born": "","Age": "","Weight": "","Creatinine": "","Height": "","Square": ""},
"prescription": [{"Medicament": "", "Poso": "","Voie":"","BiZone":""}],
"double_line_square": "",
"unknown": [{"field}:""}],
"extra":""
"""
DEFAULT_PROMPT_CONFIG = {
    'system_prompt': DEFAULT_SYSTEM_PROMPT,
}


prompts = {
    models.DETECTION_PRESCRIPTION: _prescription_prompt_template,
}

max_sizes = {
    'google': 4096000,     #  4 MB
    'openai': 20480000,    # 20 MB
    'anthropic': 5242880,  #  5 MB
}
tiff_sigs = ['49492a00']
png_sigs = ['89504e47']
jpg_sigs = ['ffd8ffe0', 'ffd8ffe1', 'ffd8ffe2', 'ffd8ffee']
pdf_sigs = ['25504446']
sigs = {
    '.tiff': tiff_sigs,
    '.tif': tiff_sigs,
    '.png': png_sigs,
    '.jpg': jpg_sigs,
    '.jpeg': jpg_sigs,
}
image_extensions = list(sigs.keys())
sigs['.pdf'] = pdf_sigs
to_recognition = set(list(sigs.keys()))


def load_image(img_data: Image.Image | bytes | np.ndarray) -> Image.Image:
    if isinstance(img_data, Image.Image):
        return img_data
    if isinstance(img_data, np.ndarray):
        res = Image.fromarray(img_data, mode="RGB")
        del img_data
        return res
    res = Image.open(io.BytesIO(img_data))
    del img_data
    return res


async def transform_image(img_data, max_size=None, transforms: list = None, reduce_transforms: list = None):
    loop = asyncio.get_event_loop()
    for transform in transforms:
        img_data, ext = await loop.run_in_executor(None, transform, img_data)

    if not max_size:
        return img_data, None

    if await check_base64_size(img_data, max_size):
        return img_data, None

    img_data_old = img_data
    for transform in reduce_transforms:
        img_data_new, ext = await loop.run_in_executor(None, transform, img_data_old)
        del img_data_old
        if await check_base64_size(img_data_new, max_size):
            return img_data_new, ext
        img_data_old = img_data_new

    return None


def transform_rotate(img_data):
    logger.info('[TRANSFORM] Auto-rotate')
    img = load_image(img_data)
    detected_ext = detect_extension(img_data[:4].hex())

    if img.mode == 'RGBA':
        img = img.convert('RGB')

    img_rgb, rotate = tesseract.document_norm_rotate(image_rgb=np.array(img))
    if rotate == 0:
        # Untouched
        return img_data, None
    img = load_image(img_rgb)

    byte_io = io.BytesIO()

    if detected_ext == '.jpg':
        img.save(byte_io, 'JPEG')
    else:
        img.save(byte_io, 'PNG')

    del img, img_data, img_rgb
    return byte_io.getvalue(), detected_ext


def transform_gray(img_data):
    logger.info('[TRANSFORM] Convert to grayscale')
    img = load_image(img_data)
    img = ImageOps.grayscale(img)
    byte_io = io.BytesIO()

    img.save(byte_io, 'JPEG')
    del img, img_data
    return byte_io.getvalue(), '.jpg'


def transform_jpg(img_data):
    logger.info('[TRANSFORM] Convert to JPG')
    img = load_image(img_data)
    byte_io = io.BytesIO()

    img.save(byte_io, 'JPEG')
    del img, img_data
    return byte_io.getvalue(), '.jpg'


def transform_lower_res(img_data, factor=0.75):
    logger.info('[TRANSFORM] Scaling image 0.75x')
    img = load_image(img_data)
    img = ImageOps.scale(img, factor=factor)
    byte_io = io.BytesIO()

    img.save(byte_io, 'JPEG')
    del img, img_data
    return byte_io.getvalue(), '.jpg'


async def check_base64_size(data: [str, bytes], max_size=None):
    if not max_size:
        return True

    if isinstance(data, str):
        data = data.encode()

    file_data_bs = base64.b64encode(data).decode()
    res = len(file_data_bs) <= max_size
    del file_data_bs
    return res


def detect_extension(sig_4bytes: str) -> str | None:
    for ext, format_sigs in sigs.items():
        if sig_4bytes in format_sigs:
            return ext
    return None


def check_file_extension(file: UploadFile, only_images=False):
    name = file.filename
    file_data = file.file.read()
    type = file.content_type

    if not type:
        # Detect file type by signature
        sig = file_data[:4].hex()
        tiff = sig == tiff_sigs
        png = sig == png_sigs
        jpg = sig in [jpg_sigs]
        pdf = sig == pdf_sigs
        if not any([tiff, png, jpg, pdf]):
            if only_images:
                raise HTTPException(400, 'File is not an image or pdf type (not provided content-type)')
    elif all([
        not type.startswith('image'),
        not name.lower().endswith('.tiff'),
        not name.lower().endswith('.tif'),
        not name.lower().endswith('.pdf'),
    ]):
        if only_images:
            raise HTTPException(400, 'File is not an image or pdf type')

    file.file.seek(0)
    return file


async def convert_file_image(file: UploadFile, llm_type, auto_rotate=False, return_list=False) -> tuple[AsyncGenerator[tuple[bytes, str]], int]:
    gen = convert_file_image_count(file, llm_type, auto_rotate, return_list)
    count: int = await anext(gen)
    return gen, count


async def convert_file_image_count(file: UploadFile, llm_type, auto_rotate=False, return_list=False) -> AsyncGenerator[Union[tuple[bytes, str], int]]:
    name = file.filename
    file_data = file.file.read()
    del file
    max_size = max_sizes.get(llm_type)
    reduce_transforms = [transform_jpg, transform_lower_res, transform_lower_res, transform_gray]
    transforms = []
    if auto_rotate:
        transforms = [transform_rotate]

    detected_ext = detect_extension(file_data[:4].hex())
    base_name, ext = os.path.splitext(name)

    if detected_ext != ext and detected_ext is not None:
        logger.info(f'Detected extension {detected_ext} on file {name}. Renaming')
        name = f'{base_name}{detected_ext}'
        ext = detected_ext

    if ext in ['.tiff', '.tif'] or (len(file_data) > 4 and file_data[:4].hex() in tiff_sigs):
        new_name = f'{os.path.splitext(name)[0]}.png'
        logger.info(f"Convert {name} to {new_name}")

        img = Image.open(io.BytesIO(file_data))
        byte_io = io.BytesIO()
        img.save(byte_io, 'PNG')
        file_data = byte_io.getvalue()
        del img, byte_io
        name = new_name

    if ext == '.pdf' or (len(file_data) > 4 and file_data[:4].hex() in pdf_sigs):
        base_name = f'{os.path.splitext(name)[0]}'

        page_num = file_formats.get_pdf_page_count(stream=io.BytesIO(file_data))

        yield page_num
        for page in range(1, page_num + 1):
            logger.info(f"[CONVERT] {name} {page}/{page_num} to JPEG")
            img = pdf2image.convert_from_bytes(file_data, dpi=300, first_page=page, last_page=page)[0]
            byte_io = io.BytesIO()
            img.save(byte_io, 'JPEG')
            del img

            result_data = byte_io.getvalue()
            name = f'{base_name}_{page}.jpg'
            logger.info(f'[TRANSFORM] {name} Processing page {page} / {page_num}')
            result_data, ext = await transform_image(result_data, max_size, transforms, reduce_transforms)
            if ext:
                name = f'{os.path.splitext(name)[0]}{ext}'

            yield result_data, name

        return

    result_data, ext = await transform_image(file_data, max_size, transforms, reduce_transforms)
    if ext:
        name = f'{os.path.splitext(name)[0]}{ext}'

    yield 1
    yield result_data, name


async def process_detection(
    org, ws, llm: BaseChatModel,
    detection: models.Detection, det: models.DetectionItem, out: models.DetectionItemOutput,
    llm_type: str, access_type='session'
):
    try:
        output_dict = await _process_detection(org, ws, llm, detection, det, out, llm_type, access_type=access_type)
    except Exception as e:
        msg = f'{e.__class__.__name__}: {str(e)}'
        updated_det = {
            'output': msg,
            'status': models.STATUS_ERROR,
        }

        await db_api.update_detection_item(det, {'status': updated_det['status']})
        await db_api.update_detection_item_output(out, updated_det)
        logger.exception(msg)
    else:
        updated_det = {'status': models.STATUS_SUCCESS}
        updated_det.update(output_dict)
        if 'output' in updated_det:
            updated_det['output'] = updated_det['output'].replace('\x00', '')

        await db_api.update_detection_item(det, {'status': updated_det['status']})
        await db_api.update_detection_item_output(out, updated_det)

    await db_api.update_metrics_for_detection(det)


def build_message(file_data):
    if isinstance(file_data, str):
        return {"type": "text", "text": file_data}

    file_data_bs = base64.b64encode(file_data)
    ext = detect_extension(file_data[:4].hex())

    mimetype = mimetypes.guess_type(f'a{ext}')[0]
    return {
        "type": "image_url", "image_url": {"url": f"data:{mimetype};base64,{file_data_bs.decode()}"}
    }


async def _extract_pdf_one_shot(filename, file_data, llm_type, llm, doc_prompt, extract_prompt, page_count):
    """Extract PDF using one-shot approach (≤6 pages)."""
    file_struct = UploadFile(io.BytesIO(file_data), size=len(file_data), filename=filename)
    file_datas, file_count = await convert_file_image(file_struct, llm_type, auto_rotate=True, return_list=True)

    if extract_prompt:
        # Collect all page data and use extract_prompt
        all_page_data = []
        async for data, name in file_datas:
            all_page_data.append(data)

        message_content = [{"type": "text", "text": extract_prompt}]
        for page_data in all_page_data:
            message_content.append(build_message(page_data))

        messages = [HumanMessage(content=message_content)]
        output = await invoke_with_retry(llm, messages)
        logger.info(f'One-shot PDF extraction: {output.content}')
        docs = file_formats.parse_as_text(output.content, filename, page=1, total_pages=page_count)

        # Clean up
        for data in all_page_data:
            del data
        del message_content
    else:
        # Use doc_prompt page by page
        docs = []
        i = 0
        async for data, name in file_datas:
            message_content = [
                {"type": "text", "text": doc_prompt},
                build_message(data)
            ]
            messages = [HumanMessage(content=message_content)]
            output = await invoke_with_retry(llm, messages)
            logger.info(f'[{i+1}/{file_count}] Recognized doc: {output.content}')
            sub_docs = file_formats.parse_as_text(output.content, filename, page=i+1, total_pages=file_count)
            docs.extend(sub_docs)
            del data
            del message_content
            i += 1

    return docs


async def _extract_pdf_double_shot(filename, file_data, llm_type, llm, doc_prompt, extract_prompt, page_count):
    """Extract PDF using double-shot approach (>6 pages)."""
    file_struct = UploadFile(io.BytesIO(file_data), size=len(file_data), filename=filename)
    file_datas, file_count = await convert_file_image(file_struct, llm_type, auto_rotate=True, return_list=True)

    # First shot: always extract plain data page by page using doc_prompt
    docs = []
    i = 0
    async for data, name in file_datas:
        message_content = [
            {"type": "text", "text": doc_prompt},
            build_message(data)
        ]
        messages = [HumanMessage(content=message_content)]
        output = await invoke_with_retry(llm, messages)
        logger.info(f'[{i+1}/{file_count}] Plain extraction: {output.content}')
        sub_docs = file_formats.parse_as_text(output.content, filename, page=i+1, total_pages=file_count)
        docs.extend(sub_docs)
        del data
        del message_content
        i += 1

    # Second shot: conditionally use extract_prompt on combined data
    if extract_prompt:
        combined_text = '\n\n'.join([d.page_content for d in docs])
        message_content = [
            {"type": "text", "text": extract_prompt},
            build_message(combined_text)
        ]
        messages = [HumanMessage(content=message_content)]
        output = await invoke_with_retry(llm, messages)
        logger.info(f'Double-shot final extraction: {output.content}')
        docs = file_formats.parse_as_text(output.content, filename, page=1, total_pages=page_count)
        del message_content

    return docs


async def _extract_images(filename, file_data, llm_type, llm, extraction_prompt):
    """Extract data from image files."""
    file_struct = UploadFile(io.BytesIO(file_data), size=len(file_data), filename=filename)
    file_datas, file_count = await convert_file_image(file_struct, llm_type, auto_rotate=True, return_list=True)

    # Check if the model supports system prompts
    model_name = llm_utils.get_model_name(llm)
    config = {'model_name': model_name} if model_name else {}
    supports_system_message = llm_utils.support_system_message(config)

    docs = []
    i = 0
    async for data, name in file_datas:
        if supports_system_message:
            messages = [SystemMessage(content=extraction_prompt), HumanMessage(content=[build_message(data)])]
        else:
            messages = [HumanMessage(content=extraction_prompt), HumanMessage(content=[build_message(data)])]

        output = await invoke_with_retry(llm, messages)
        # logger.info(f'[{i+1}/{file_count}] Recognized doc: {output.content}')
        sub_docs = file_formats.parse_as_text(output.content, filename, page=i+1, total_pages=file_count)
        docs.extend(sub_docs)
        del data
        i += 1

    return docs


async def _extract_pdf_page_text(filename, file_data, page_num, total_pages):
    """Extract text from a single PDF page without images."""
    loop = asyncio.get_event_loop()

    def extract_page_text():
        doc = pymupdf.open(stream=file_data, filetype='pdf')
        text = doc.get_page_text(page_num)
        doc.close()
        return text

    page_text = await loop.run_in_executor(None, extract_page_text)
    return file_formats.parse_as_text(page_text, filename, page=page_num+1, total_pages=total_pages)


async def _extract_pdf_page_image(filename, file_data, page_num, total_pages, llm_type, llm, doc_prompt) -> list[Document]:
    """Extract text from a single PDF page with images using LLM and doc_prompt."""
    loop = asyncio.get_event_loop()

    def convert_page_to_image():
        img = pdf2image.convert_from_bytes(file_data, dpi=300, first_page=page_num+1, last_page=page_num+1)[0]
        byte_io = io.BytesIO()
        img.save(byte_io, 'JPEG')
        del img
        return byte_io.getvalue()

    img_data = await loop.run_in_executor(None, convert_page_to_image)

    # Apply transforms if needed
    max_size = max_sizes.get(llm_type)
    reduce_transforms = [transform_jpg, transform_lower_res, transform_lower_res, transform_gray]
    transforms = [transform_rotate]  # auto_rotate=True equivalent

    img_data, ext = await transform_image(img_data, max_size, transforms, reduce_transforms)
    if img_data is None:
        # Fallback to text extraction if image processing fails
        return await _extract_pdf_page_text(filename, file_data, page_num, total_pages)

    # Use doc_prompt to extract plain text from the image
    message_content = [
        {"type": "text", "text": doc_prompt},
        build_message(img_data)
    ]
    messages = [HumanMessage(content=message_content)]
    output = await invoke_with_retry(llm, messages)
    logger.info(f'[{page_num+1}/{total_pages}] Mixed LLM page with images (plain text extraction): {output.content}')

    del img_data
    del message_content

    return file_formats.parse_as_text(output.content, filename, page=page_num+1, total_pages=total_pages)


async def _extract_pdf_mixed_llm(filename, file_data, llm_type, llm, doc_prompt, extract_prompt):
    """Extract PDF using mixed LLM approach: images -> LLM, text-only -> direct text extraction.

    First shot: Extract plain text from all pages (LLM for image pages, direct extraction for text-only pages)
    Second shot: Conditionally apply extract_prompt to combined text
    """
    # Get page count and image map
    page_count = file_formats.get_pdf_page_count(stream=io.BytesIO(file_data))
    page_map = file_formats.get_pdf_image_map(stream=io.BytesIO(file_data))

    logger.info(f'Mixed LLM processing PDF with {page_count} pages.')

    # First shot: Extract plain text from all pages
    docs = []
    for page_num in range(page_count):
        if page_map[page_num]:
            # Page contains images -> use LLM with doc_prompt to extract text
            logger.info(f'[{page_num+1}/{page_count}] Processing page with images using LLM')
            page_docs = await _extract_pdf_page_image(filename, file_data, page_num, page_count, llm_type, llm, doc_prompt)
        else:
            # Page has no images -> extract text directly
            logger.info(f'[{page_num+1}/{page_count}] Processing text-only page using direct text extraction')
            page_docs = await _extract_pdf_page_text(filename, file_data, page_num, page_count)

        docs.extend(page_docs)

    # Second shot: Conditionally apply extract_prompt to combined text
    if extract_prompt:
        combined_text = '\n\n'.join([d.page_content for d in docs])
        message_content = [
            {"type": "text", "text": extract_prompt},
            build_message(combined_text)
        ]
        messages = [HumanMessage(content=message_content)]
        output = await invoke_with_retry(llm, messages)
        logger.info(f'Mixed LLM final extraction with extract_prompt: {output.content}')
        docs = file_formats.parse_as_text(output.content, filename, page=1, total_pages=page_count)
        del message_content

    return docs


async def extract_document_data(
    filename, file_data, llm_type, llm, doc_prompt, extract_prompt=None,
    mode: Literal['llm', 'ocr', 'text', 'force_llm', 'mixed_llm'] = 'llm'
):
    # Let the main thread go
    await asyncio.sleep(SMALL_SLEEP)

    loop = asyncio.get_event_loop()
    ext = detect_extension(file_data[:4].hex())
    logger.info(f'Detected extension {ext} on file {filename}')
    if not ext:
        _, ext = os.path.splitext(filename)

    # Determine which prompt to use for direct extraction
    direct_extract_prompt = extract_prompt if extract_prompt else doc_prompt

    # Additional check for pdf if contain images

    has_images = 'llm' in mode
    if ext.lower() == '.pdf':
        has_images = file_formats.pdf_contains_images(stream=io.BytesIO(file_data))

    # Handle different file types and modes
    if ext.lower() == '.pdf' and mode == 'mixed_llm':
        # Mixed mode for PDFs: pages with images -> convert, pages without -> text mode
        docs = await _extract_pdf_mixed_llm(filename, file_data, llm_type, llm, doc_prompt, extract_prompt)
    elif ext.lower() == '.pdf' and mode in ['llm', 'force_llm'] and has_images:
        # PDF files: choose one-shot or double-shot based on page count
        page_count = file_formats.get_pdf_page_count(stream=io.BytesIO(file_data))

        if page_count <= 6:
            docs = await _extract_pdf_one_shot(filename, file_data, llm_type, llm, doc_prompt, extract_prompt, page_count)
        else:
            docs = await _extract_pdf_double_shot(filename, file_data, llm_type, llm, doc_prompt, extract_prompt, page_count)

    elif ext.lower() in to_recognition and mode in ['llm', 'force_llm', 'mixed_llm'] and has_images:
        # Other image extensions: one-shot logic
        docs = await _extract_images(filename, file_data, llm_type, llm, direct_extract_prompt)

    else:
        load_file = functools.partial(file_formats.load_file, additional_meta={})
        docs = await loop.run_in_executor(None, load_file, file_data, filename)
        if mode == 'llm' and extract_prompt:
            # Extract using extraction_prompt
            message_content = [
                {"type": "text", "text": direct_extract_prompt},
                build_message(
                    '\n\nFILENAME: {filename}\n\n'.join([d.page_content for d in docs])
                )
            ]
            messages = [HumanMessage(content=message_content)]
            output = await llm.ainvoke(messages)
            docs = file_formats.parse_as_text(output.content, filename, page=1, total_pages=1)

        del file_data

    return docs


@retry_utils.retry(IndexError, tries=3, delay=1, jitter=2)
async def invoke_with_retry(llm, messages):
    return await llm.ainvoke(messages)


async def process_metadata(
    extractor: models.Extractor, ext_file: models.DatasetFile, file_data, llm_type, llm: BaseChatModel, prompt,
    extracted_docs: list[Document], access_type='session'
):
    try:
        metadata = await extract_document_metadata(extracted_docs, llm, prompt)
        meta = extract_json(metadata)
    except Exception as exc:
        logger.exception(str(exc))
        status = models.STATUS_ERROR
        msg = f'{exc.__class__.__name__}: {str(exc)}'
        meta = {}
    else:
        msg = None
        status = models.STATUS_SUCCESS

    json_status = {'status': status, 'message': msg}
    await db_api.update_dataset_file(
        ext_file,
        {'status': json_status, 'meta': meta}
    )
    logger.info(f"Metadata extracted: {meta}")
    await db_api.update_extractor(extractor, {'status': json_status})
    return json_status


async def extract_document_metadata(extracted_docs: list[Document], llm: BaseChatModel, prompt):
    file_data = '\n\n'.join([d.page_content for d in extracted_docs])

    # Truncate file_data to the according token limit of the model
    model_name = llm_utils.get_model_name(llm) or "gpt-4o"
    context_limit = get_model_context_limit(model_name)
    # Count tokens in the document and prompt
    doc_tokens = count_tokens(file_data, model_name)
    prompt_tokens = count_tokens(prompt, model_name)
    # Drop max tokens
    if hasattr(llm, 'max_tokens'):
        llm.max_tokens = None
    if doc_tokens + prompt_tokens + 2000 > context_limit:
        file_data = split_document(Document(
            page_content=file_data), context_limit - prompt_tokens - 2000, model_name
        )[0].page_content

    messages = [
        SystemMessage(content=prompt),
        HumanMessage(content=[build_message(file_data)])
    ]
    output = await llm.ainvoke(messages)
    return output.content


async def _process_keep_files(access_type, config, file_db, file_path):
    delete_file_api = access_type in ['token--workspace', 'token--user'] and not config.get('keep_files_api', False)
    delete_file_ui = access_type not in ['token--workspace', 'token--user'] and not config.get('keep_files', True)
    if delete_file_ui or delete_file_api:
        # TODO temporary fix
        if not file_db.source_url:
            await SharedConfig().file_manager.delete_file(file_path)
        await db_api.update_detection_file(file_db.id, {'status': models.STATUS_DELETED})


async def _process_detection(
    org, ws, llm: BaseChatModel,
    detection: models.Detection, det: models.DetectionItem, out: models.DetectionItemOutput,
    llm_type: str, access_type='session'
) -> dict:
    if det.type in ['prescription', 'prompt']:
        return await _process_prompt_detection(org, ws, llm, detection, det, out, llm_type, access_type)
    elif det.type == 'posology':
        return await _process_posology_detection(org, ws, llm, detection, det, out, llm_type, access_type)
    elif det.type == 'barcode':
        return await _process_barcode_detection(org, ws, llm, detection, det, out, llm_type, access_type)
    elif det.type == 'smart_merge':
        return await _process_smart_merge_detection(org, ws, llm, detection, det, out, llm_type, access_type)
    else:
        raise ValueError(f"Unrecognized detection type '{det.type}'")


async def _process_prompt_detection(
    org, ws, llm: BaseChatModel,
    detection: models.Detection, det: models.DetectionItem, out: models.DetectionItemOutput,
    llm_type: str, access_type='session'
) -> dict:
    config = detection.get_config()
    prompts_config = config.get('prompts', {})
    system_prompt = prompts_config.get('system_prompt') or DEFAULT_SYSTEM_PROMPT

    # Get doc_prompt and extract_prompt from configuration
    doc_prompt = prompts_config.get('doc_prompt') or DOCUMENT_PROMPT
    extract_prompt = prompts_config.get('extract_prompt') or system_prompt

    files = await db_api.list_detection_files(detection_item_id=det.id)
    files = files or []
    if len(files) < 1:
        raise ValueError('Uploaded file not found')
    file = files[0]

    if not file.source_url:
        path = get_detection_file_path(org.name, ws.name, file)
        result = await SharedConfig().file_manager.read_file(path)
        file_data = await SharedConfig().file_manager.get_data(result)
    else:
        path = None
        file_data = await download_content(file.source_url)

    t = time.time()
    # Use extract_document_data to process the file
    docs = await extract_document_data(
        filename=file.name,
        file_data=file_data,
        llm_type=llm_type,
        llm=llm,
        doc_prompt=doc_prompt,
        extract_prompt=extract_prompt,
        mode=config.get('extraction_mode') or 'llm'
    )
    elapsed = time.time() - t

    await _process_keep_files(access_type, config, file, path)

    # Combine all document content
    output_content = '\n\n'.join([d.page_content for d in docs])
    logger.info(f"[DETECTION] Output: {output_content}")

    await metric_utils.squash_metrics(org.id, ws.id, det.detection_id, models.APP_TYPE_DETECTION, out.id, set_date=out.updated_at)
    await metric_utils.ensure_metric(
        org.id, ws.id, det.detection_id, out.id, models.METRIC_RESPONSE_TIME, elapsed,
        app_type=models.APP_TYPE_DETECTION, set_date=out.updated_at,
    )

    return {'output': output_content}


async def _process_smart_merge_detection(
    org, ws, llm: BaseChatModel,
    detection: models.Detection, det: models.DetectionItem, out: models.DetectionItemOutput,
    llm_type: str, access_type='session'
) -> dict:
    config = detection.get_config()
    prompt = (config.get('prompts') or {}).get('system_prompt') or smart_merge.MERGE_LISTS_PROMPT

    files = await db_api.list_detection_files(detection_item_id=det.id)
    files = files or []
    if len(files) < 1:
        raise ValueError('Uploaded file not found')
    file = files[0]

    if not file.source_url:
        path = get_detection_file_path(org.name, ws.name, file)
        result = await SharedConfig().file_manager.read_file(path)
        file_data = await SharedConfig().file_manager.get_data(result)
        _, ext = os.path.splitext(file.name)
        if ext.lower() != '.json':
            raise ValueError('This detection type can only work with .json files with keys "left" and "right"')
    else:
        path = None
        file_data = await download_content(file.source_url)

    json_data = json.loads(file_data)

    if 'left' not in json_data or 'right' not in json_data:
        raise ValueError('This detection type can only work with .json files with keys "left" and "right"')

    t = time.time()
    result = await smart_merge.adict_merge(
        json_data['left'], json_data['right'],
        mode='overwrite_llm', check_keys=False,
        llm=llm, verbose=config.get('detection_verbose', False),
        prompt=prompt,
    )
    elapsed = time.time() - t
    await _process_keep_files(access_type, config, file, path)

    result = json.dumps(result, indent=2)
    logger.info(f"[DETECTION] Output: {result}")
    result = f'```json\n{result}\n```'

    await metric_utils.squash_metrics(org.id, ws.id, det.detection_id, models.APP_TYPE_DETECTION, out.id, set_date=out.updated_at)
    await metric_utils.ensure_metric(
        org.id, ws.id, det.detection_id, out.id, models.METRIC_RESPONSE_TIME, elapsed,
        app_type=models.APP_TYPE_DETECTION, set_date=out.updated_at,
    )

    return {'output': result}


async def gen_item(item):
    yield item


# noinspection PyInconsistentReturns
async def _process_posology_detection(
    org, ws, llm: BaseChatModel,
    detection: models.Detection, det: models.DetectionItem, out: models.DetectionItemOutput,
    llm_type: str, access_type='session'
) -> dict[str, Any]:
    config = detection.get_config()
    start_text = config.get('posology', {}).get('start_text', r'\n[|#*_\s]*4.2[*_.\s]+Poso')
    end_text = config.get('posology', {}).get('end_text', r'\n[|#*_\s]*4.3[*_.\s]+Contr')
    from_line_start = config.get('posology', {}).get('from_line_start', True)
    section_pattern = config.get('posology', {}).get('section_pattern', r'rubriques?\s+[0-9.,\set]+[0-9.,]')
    image_dpi = int(config.get('posology', {}).get('image_dpi', 100))
    style_map = config.get('posology', {}).get('styles_map', {
        '.AmmAnnexeTitre1': 'h2',
        '.AmmAnnexeTitre2': 'h3',
        '.AmmAnnexeTitre3': 'h4',
        '.AmmAnnexeTitre4': 'h5',
        '.AmmCorpsTexteGras': 'b',
    })

    files = await db_api.list_detection_files(detection_item_id=det.id)
    files = files or []
    if len(files) < 1:
        raise ValueError('Uploaded file not found')

    file = files[0]

    base, ext = os.path.splitext(file.name)
    if ext not in ['.pdf', '.html', '.htm']:
        raise ValueError('Currently posology detector works only with PDF and HTML files.')

    if not file.source_url:
        path = get_detection_file_path(org.name, ws.name, file)
        result = await SharedConfig().file_manager.read_file(path)
        file_data = await SharedConfig().file_manager.get_data(result)
    else:
        path = None
        file_data = await download_content(file.source_url)

    more_kwargs = {}
    if ext.lower() == '.pdf':
        extract_func = posology.extract_section_pymupdf4llm
    else:
        extract_func = posology.extract_section_html
        more_kwargs = {'url': file.source_url}

    dirname, saved_path = posology.save_to_tmp_dir(data=file_data, name=file.name)

    zip_file = io.BytesIO()
    write_metrics_handle = write_metrics(
        ws.id,
        org.id,
        models.APP_TYPE_DETECTION,
        det.detection_id,
        out.id,
        model_name=None,
    )
    try:
        # output = await loop.run_in_executor(None, extract_section_pymupdf4llm, saved_path, zip_file)
        wrapped_func = write_metrics_handle(extract_func)
        output = await wrapped_func(
            saved_path,
            zip_file,
            start_text=start_text,
            end_text=end_text,
            from_line_start=from_line_start,
            section_pattern=section_pattern,
            image_dpi=image_dpi,
            style_map=style_map,
            **more_kwargs
        )
    finally:
        # Cleanup
        shutil.rmtree(dirname)

    await _process_keep_files(access_type, config, file, path)

    logger.info(f"[POSOLOGY] Output: {output}")

    zip_data = zip_file.getvalue()
    output_file = await db_api.create_detection_file({
        'detection_item_id': 0,
        'name': f'{base}.zip',
        'size': len(zip_data),
        'status': models.STATUS_SUCCESS,
        'owner_id': det.owner_id,
        'workspace_id': det.workspace_id,
    })
    output_file_path = get_detection_file_path(org_name=org.name, workspace_name=ws.name, file=output_file)
    await SharedConfig().file_manager.save_file(output_file_path, zip_data)

    return {'output_file_id': output_file.id, 'output': output}


# noinspection PyInconsistentReturns
async def _process_barcode_detection(
    org, ws, llm: BaseChatModel,
    detection: models.Detection, det: models.DetectionItem, out: models.DetectionItemOutput,
    llm_type: str, access_type='session'
) -> dict[str, Any]:
    config = detection.get_config()
    image_dpi = int(config.get('barcode', {}).get('image_dpi', 324))
    all_images = bool(config.get('barcode', {}).get('extract_all_images', True))

    files = await db_api.list_detection_files(detection_item_id=det.id)
    files = files or []
    if len(files) < 1:
        raise ValueError('Uploaded file not found')

    file = files[0]
    if not file.source_url:
        path = get_detection_file_path(org.name, ws.name, file)
        result = await SharedConfig().file_manager.read_file(path)
        file_data = await SharedConfig().file_manager.get_data(result)
    else:
        path = None
        file_data = await download_content(file.source_url)

    base, ext = os.path.splitext(file.name)
    if ext != '.pdf':
        raise ValueError('Currently posology detector works only with PDF files.')

    dirname, saved_path = posology.save_to_tmp_dir(data=file_data, name=file.name)

    zip_file = io.BytesIO()
    write_metrics_handle = write_metrics(
        ws.id,
        org.id,
        models.APP_TYPE_DETECTION,
        det.detection_id,
        out.id,
        model_name=None,
    )
    try:
        # output = await loop.run_in_executor(None, extract_section_pymupdf4llm, saved_path, zip_file)
        wrapped_func = write_metrics_handle(barcode.extract_barcodes)
        output = await wrapped_func(
            saved_path,
            zip_file,
            image_dpi=image_dpi,
            impl='opencv',
            extract_all_images=all_images,
        )
    finally:
        # Cleanup
        shutil.rmtree(dirname)

    await _process_keep_files(access_type, config, file, path)

    logger.info(f"[BARCODE] Detected barcodes in {file.name}: {output}")

    zip_data = zip_file.getvalue()
    output_file = await db_api.create_detection_file({
        'detection_item_id': 0,
        'name': f'{base}.zip',
        'size': len(zip_data),
        'status': models.STATUS_SUCCESS,
        'owner_id': det.owner_id,
        'workspace_id': det.workspace_id,
    })
    output_file_path = get_detection_file_path(org_name=org.name, workspace_name=ws.name, file=output_file)
    await SharedConfig().file_manager.save_file(output_file_path, zip_data)

    return {'output_file_id': output_file.id, 'output': output}


def get_detection_file_path(org_name: str, workspace_name: str, file: models.DetectionItemFile):
    name, ext = os.path.splitext(file.name)
    return f'detection_files/{org_name}/{workspace_name}/{file.workspace_id}/{file.name}/{file.id}{ext}'


def get_extractor_file_path(org_name: str, workspace_name: str, file: models.ExtractorFile):
    name, ext = os.path.splitext(file.name)
    return f'extractor_files/{org_name}/{workspace_name}/{file.workspace_id}/{file.name}/{file.id}{ext}'
