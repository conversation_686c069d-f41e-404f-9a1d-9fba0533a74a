#!/usr/bin/env python3
"""
Migration script to move extractor and timeline collections to unified workspace collections.

This script:
1. Processes all workspaces in the system (or specific workspace if provided)
2. Migrates extractor and timeline collections to unified workspace collections
3. Cleans up old collections immediately after migration
4. Updates database configurations

Usage:
    python scripts/migrate_unified_collections.py [--dry-run] [--workspace-id ID]
"""

import argparse
import asyncio
import datetime
import logging
from typing import List, Dict, Any, Optional, Tuple, Union

from qdrant_client import models as qdrant_models

from ira_chat.db import api as db_api, models, base
from ira_chat.services import vectorstore as ira_vectorstore, langchain_svc, engine_manager
from ira_chat.services.langchain_svc import _embeddings_size
from ira_chat.utils import utils


logger = logging.getLogger(__name__)


def get_vector_dimension_for_config(embedding_provider: str, embedding_model: str = None) -> int:
    """
    Detect vector dimension based on embedding provider and model.

    Args:
        embedding_provider: The embedding provider (e.g., 'openai', 'ollama')
        embedding_model: Specific model name (optional)

    Returns:
        Vector dimension for the embedding model
    """
    try:
        # Create temporary config to get embedding instance
        temp_config = {'llm_type': embedding_provider}
        if embedding_model:
            temp_config['embedding_model'] = embedding_model

        # Get embedding instance and detect dimension
        embeddings = ira_vectorstore.get_embedding_model(temp_config, 0, 0)  # Temp org/workspace IDs
        return _embeddings_size(embeddings)

    except Exception as e:
        logger.warning(f"Could not detect dimension for {embedding_provider}/{embedding_model}: {str(e)}")
        # Fallback to common default
        return 1536


async def detect_vector_dimension_from_index(
    index: models.DatasetIndex,
    dataset: models.Dataset,
    vectorstore: Any
) -> int:
    """
    Detect vector dimension from existing Qdrant collection or config.

    Args:
        index: DatasetIndex to analyze
        dataset: Associated Dataset
        vectorstore: Vectorstore instance

    Returns:
        Vector dimension for the index
    """
    # First, check if dimension is already stored in config
    config = index.get_config()
    if config.get('vector_dimension'):
        return config['vector_dimension']

    # Try to detect from existing Qdrant collection
    try:
        org = await db_api.get_org_by_id(dataset.org_id)
        source_collection_name = engine_manager.index_namespace_suffix(org, dataset, index.name)
        source_collection_full = ira_vectorstore.get_index_namespace(source_collection_name)

        client = vectorstore.client
        if client.collection_exists(source_collection_full):
            collection_info = client.get_collection(source_collection_full)
            # Access vector dimension from collection config
            if hasattr(collection_info.config, 'params') and hasattr(collection_info.config.params, 'vectors'):
                return collection_info.config.params.vectors.size

    except Exception as e:
        logger.warning(f"Could not detect dimension from collection for index {index.name}: {str(e)}")

    # Fallback: detect from embedding provider configuration
    embedding_provider = getattr(index, 'embedding_provider', 'openai')
    embedding_model = config.get('embedding_model')

    return get_vector_dimension_for_config(embedding_provider, embedding_model)


async def group_indexes_by_vector_dimension(
    indexes: List[models.DatasetIndex],
    datasets: List[models.Dataset],
    vectorstore: Any
) -> Dict[int, List[Tuple[models.DatasetIndex, models.Dataset]]]:
    """
    Group indexes by their vector dimensions.

    Args:
        indexes: List of DatasetIndex objects
        datasets: List of Dataset objects
        vectorstore: Vectorstore instance

    Returns:
        Dict mapping vector_dimension -> list of (index, dataset) tuples
    """
    dimension_groups = {}
    dataset_map = {d.id: d for d in datasets}

    for index in indexes:
        dataset = dataset_map.get(index.dataset_id)
        if not dataset:
            logger.warning(f"Dataset not found for index {index.name}")
            continue

        vector_dim = await detect_vector_dimension_from_index(index, dataset, vectorstore)

        if vector_dim not in dimension_groups:
            dimension_groups[vector_dim] = []
        dimension_groups[vector_dim].append((index, dataset))

        logger.info(f"Index {index.name} grouped with dimension {vector_dim}")

    return dimension_groups


async def migrate_all_workspaces(dry_run: bool = True) -> Dict[str, Any]:
    """
    Migrate all workspaces' extractor/timeline collections to unified collections.
    
    Args:
        dry_run: If True, only log what would be done without making changes
        
    Returns:
        Dict with migration summary
    """
    logger.info(f"Starting migration for ALL workspaces (dry_run={dry_run})")
    
    # Get all organizations
    orgs = await db_api.list_orgs_all()
    logger.info(f"Found {len(orgs)} organizations")
    
    total_summary = {
        'workspaces_processed': 0,
        'total_documents_migrated': 0,
        'collections_migrated': 0,
        'errors': []
    }
    
    for org in orgs:
        workspaces = await db_api.list_workspaces(org_id=org.id)
        logger.info(f"Processing {len(workspaces)} workspaces for org: {org.name}")
        
        for workspace in workspaces:
            result = await migrate_workspace_to_unified_collections(workspace, org, dry_run)
            total_summary['workspaces_processed'] += 1
            total_summary['total_documents_migrated'] += result['total_documents']
            total_summary['collections_migrated'] += result['collections_migrated']
                
    logger.info(f"Migration completed. Summary: {total_summary}")
    return total_summary


async def collect_extractor_timeline_datasets(workspace_id: int) -> Tuple[Dict[str, Union[models.Extractor, Tuple[models.Timeline, models.Extractor]]], List[str]]:
    """
    Collect and map all extractor and timeline datasets for a workspace.

    Args:
        workspace_id: ID of workspace to process

    Returns:
        Tuple of (extractor_map, dataset_ids_list) where:
        - extractor_map: dataset_id -> extractor or (timeline, extractor)
        - dataset_ids_list: list of dataset IDs that need migration
    """
    # Find all extractors and timelines for this workspace
    extractors, _ = await db_api.list_extractors(workspace_id=workspace_id)
    timelines, _ = await db_api.list_timelines(workspace_id=workspace_id)

    if not extractors and not timelines:
        # logger.info("No extractors or timelines found for workspace")
        return {}, []
    logger.info(f"Found {len(extractors)} extractors and {len(timelines)} timelines")

    # Collect all dataset IDs that need migration
    dataset_ids = set()
    extractor_map = {}  # dataset_id -> extractor or (timeline, extractor)

    # Process regular extractors
    for extractor in extractors:
        if hasattr(extractor, 'auto_dataset_id') and extractor.auto_dataset_id:
            dataset_ids.add(extractor.auto_dataset_id)
            extractor_map[extractor.auto_dataset_id] = extractor

    # Process timeline extractors
    for timeline in timelines:
        timeline_points = await db_api.list_timeline_points(timeline_id=timeline.id)
        for point in timeline_points:
            extractor = await db_api.get_extractor_by_id(point.extractor_id)
            if hasattr(extractor, 'auto_dataset_id') and extractor.auto_dataset_id:
                dataset_ids.add(extractor.auto_dataset_id)
                extractor_map[extractor.auto_dataset_id] = (timeline, extractor)

    return extractor_map, list(dataset_ids)


async def initialize_unified_collection(
    org: models.Organization,
    workspace: models.Workspace,
    vector_dimension: int,
    vectorstore: Any,
    dry_run: bool = True
) -> str:
    """
    Initialize and prepare dimension-specific unified collection.

    Args:
        org: Organization object
        workspace: Workspace object
        vector_dimension: Vector dimension for this collection
        vectorstore: Vectorstore instance (already initialized)
        dry_run: If True, skip actual collection creation

    Returns:
        unified_collection_name
    """
    unified_collection_name = f"{org.name}-{workspace.name}-{vector_dimension}"
    logger.info(f"Target unified collection: {unified_collection_name} (dimension: {vector_dimension})")

    # Create unified collection if needed
    if not dry_run:
        unified_namespace = ira_vectorstore.get_index_namespace(unified_collection_name)
        langchain_svc.prepare_vectorstore(vectorstore, unified_namespace, index_recreate=False)
        logger.info(f"Prepared unified collection: {unified_collection_name}")

    return unified_collection_name


async def orchestrate_dimension_aware_migration(
    org: models.Organization,
    workspace: models.Workspace,
    datasets: List[models.Dataset],
    extractor_map: Dict[str, Union[models.Extractor, Tuple[models.Timeline, models.Extractor]]],
    vectorstore: Any,
    dry_run: bool = True
) -> Tuple[int, int, Dict[str, int]]:
    """
    Orchestrate the migration of all datasets to dimension-specific unified collections.

    Args:
        org: Organization object
        workspace: Workspace object
        datasets: List of datasets to migrate
        extractor_map: Mapping of dataset_id to extractor info
        vectorstore: Vectorstore instance
        dry_run: If True, only count documents without migrating

    Returns:
        Tuple of (total_documents_migrated, collections_migrated, index_dimension_map)
    """
    total_documents = 0
    collections_migrated = 0
    index_dimension_map = {}  # Track index_id -> vector_dimension

    # Get all indexes from datasets
    all_indexes = []
    for dataset in datasets:
        indexes = await db_api.list_dataset_indexes(dataset_id=dataset.id)
        all_indexes.extend(indexes)

    if not all_indexes:
        logger.info("No indexes found for migration")
        return 0, 0, {}

    # Group indexes by vector dimension
    dimension_groups = await group_indexes_by_vector_dimension(all_indexes, datasets, vectorstore)

    logger.info(f"Found {len(dimension_groups)} vector dimension groups: {list(dimension_groups.keys())}")

    # Log dimension distribution for validation
    for dim, pairs in dimension_groups.items():
        index_names = [f"{pair[1].name}/{pair[0].name}" for pair in pairs]
        logger.info(f"Dimension {dim}: {len(pairs)} indexes - {', '.join(index_names[:3])}{'...' if len(index_names) > 3 else ''}")

    # Process each dimension group separately
    for vector_dimension, index_dataset_pairs in dimension_groups.items():
        logger.info(f"Processing dimension group {vector_dimension} with {len(index_dataset_pairs)} indexes")

        # Initialize unified collection for this dimension
        unified_collection_name = await initialize_unified_collection(
            org, workspace, vector_dimension, vectorstore, dry_run
        )

        # Migrate all indexes in this dimension group
        for index, dataset in index_dataset_pairs:
            extractor_info = extractor_map.get(dataset.id)
            if extractor_info:
                docs_migrated = await migrate_index_to_unified(
                    index, dataset, unified_collection_name, extractor_info, vectorstore, vector_dimension, dry_run
                )
                total_documents += docs_migrated
                collections_migrated += 1
                # Track dimension for config update
                index_dimension_map[index.id] = vector_dimension

    return total_documents, collections_migrated, index_dimension_map


async def migrate_workspace_to_unified_collections(
    workspace: models.Workspace, 
    org: Optional[models.Organization] = None, 
    dry_run: bool = True
) -> Dict[str, Any]:
    """
    Migrate a workspace's extractor/timeline collections to unified collection.
    
    Args:
        workspace: Workspace object
        org: Organization object (optional, will be fetched if not provided)
        dry_run: If True, only log what would be done without making changes
        
    Returns:
        Dict with migration results
    """
    logger.info(f"Starting migration for workspace {workspace.id} (dry_run={dry_run})")

    if not org:
        org = await db_api.get_org_by_id(workspace.org_id)

    # Collect extractor and timeline datasets
    extractor_map, dataset_ids = await collect_extractor_timeline_datasets(workspace.id)

    if not dataset_ids:
        # logger.info("No datasets found for migration")
        return {'total_documents': 0, 'collections_migrated': 0}

    # Get datasets to migrate
    datasets = await get_datasets_by_ids(dataset_ids)
    logger.info(f"Found {len(datasets)} datasets to migrate")

    # Initialize vectorstore (single instance for all dimension groups)
    org_config = org.get_config()
    vectorstore = ira_vectorstore.get_vectorstore('langchain', org_config, org.id, workspace.id)

    # Orchestrate the dimension-aware migration process
    total_documents, collections_migrated, index_dimension_map = await orchestrate_dimension_aware_migration(
        org, workspace, datasets, extractor_map, vectorstore, dry_run
    )

    # Update database configurations with vector dimension information
    if not dry_run and index_dimension_map:
        await update_indexes_for_unified_collections(index_dimension_map)
    
    # Log migration summary with dimension information
    if index_dimension_map:
        dimension_summary = {}
        for index_id, dimension in index_dimension_map.items():
            dimension_summary[dimension] = dimension_summary.get(dimension, 0) + 1
        logger.info(f"Migration summary by dimension: {dimension_summary}")

    logger.info(f"Workspace {workspace.id} migration completed. Documents: {total_documents}, Collections: {collections_migrated}")
    return {'total_documents': total_documents, 'collections_migrated': collections_migrated}


async def validate_source_collection_and_count_documents(
    index: models.DatasetIndex,
    dataset: models.Dataset,
    vectorstore: Any
) -> Tuple[str, str, int]:
    """
    Validate source collection exists and count documents.

    Args:
        index: DatasetIndex to validate
        dataset: Associated Dataset
        vectorstore: Vectorstore instance

    Returns:
        Tuple of (source_collection_name, source_collection_full, document_count)

    Raises:
        Exception: If collection doesn't exist or can't be accessed
    """
    # Get organization for this dataset
    org = await db_api.get_org_by_id(dataset.org_id)

    # Determine source collection name using existing logic
    source_collection_name = engine_manager.index_namespace_suffix(
        org,
        dataset,
        index.name
    )
    source_collection_full = ira_vectorstore.get_index_namespace(source_collection_name)

    client = vectorstore.client

    # Check if source collection exists
    try:
        if not client.collection_exists(source_collection_full):
            logger.warning(f"Source collection {source_collection_full} does not exist")
            return source_collection_name, source_collection_full, 0

        # Count documents
        collection_info = client.get_collection(source_collection_full)
        document_count = collection_info.points_count
        logger.info(f"Found {document_count} documents in {source_collection_full}")

        return source_collection_name, source_collection_full, document_count

    except Exception as e:
        logger.error(f"Error accessing source collection {source_collection_full}: {str(e)}")
        raise


async def process_document_batch_with_metadata_enhancement(
    points: List[Any],
    extractor_info: Union[models.Extractor, Tuple[models.Timeline, models.Extractor]],
    dataset: models.Dataset,
    vector_dimension: int
) -> List[qdrant_models.PointStruct]:
    """
    Process a batch of documents and enhance their metadata for unified collection.

    Args:
        points: List of document points from source collection
        extractor_info: Extractor or (Timeline, Extractor) tuple
        dataset: Associated Dataset
        vector_dimension: Vector dimension for validation and metadata

    Returns:
        List of enhanced PointStruct objects
    """
    enhanced_points = []

    for point in points:
        enhanced_metadata = point.payload.get('metadata', {}).copy()

        # Add filtering metadata based on extractor type
        if isinstance(extractor_info, tuple):
            # Timeline extractor
            timeline, extractor = extractor_info
            enhanced_metadata.update({
                'timeline_id': timeline.id,
                'extractor_id': extractor.id,
                'subcollection_type': 'timeline',
                'workspace_id': dataset.workspace_id,
            })
        else:
            # Regular extractor
            extractor = extractor_info
            enhanced_metadata.update({
                'extractor_id': extractor.id,
                'subcollection_type': 'extractor',
                'workspace_id': dataset.workspace_id,
            })

        # Create enhanced point
        enhanced_point = qdrant_models.PointStruct(
            id=point.id,
            vector=point.vector,
            payload={
                **point.payload,
                'metadata': enhanced_metadata
            }
        )
        enhanced_points.append(enhanced_point)

    return enhanced_points


async def upload_documents_and_cleanup(
    client: Any,
    source_collection_full: str,
    unified_collection_full: str,
    enhanced_points: List[qdrant_models.PointStruct],
    migrated_count: int,
    document_count: int,
    cleanup_after_migration: bool = True
) -> int:
    """
    Upload enhanced documents to unified collection and optionally cleanup source.

    Args:
        client: Qdrant client
        source_collection_full: Full name of source collection
        unified_collection_full: Full name of unified collection
        enhanced_points: List of enhanced document points
        migrated_count: Current count of migrated documents
        document_count: Total expected document count
        cleanup_after_migration: Whether to delete source collection after migration

    Returns:
        Updated migrated_count

    Raises:
        Exception: If upload fails or migration validation fails
    """
    # Validate enhanced points before upload
    if not enhanced_points:
        logger.warning(f"No enhanced points created for batch")
        return migrated_count

    # Upload enhanced points to unified collection
    try:
        client.upsert(
            collection_name=unified_collection_full,
            points=enhanced_points
        )
    except Exception as e:
        logger.error(f"Error uploading batch to unified collection: {str(e)}")
        raise

    migrated_count += len(enhanced_points)
    progress_pct = (migrated_count / document_count * 100) if document_count > 0 else 100
    logger.info(f"Migrated {migrated_count}/{document_count} documents ({progress_pct:.1f}%)")

    # If this is the final batch, validate and cleanup
    if migrated_count >= document_count and cleanup_after_migration:
        # Validate migration was successful
        if migrated_count != document_count:
            logger.error(f"Migration incomplete: expected {document_count}, migrated {migrated_count}")
            raise ValueError(f"Migration failed: document count mismatch")

        # Clean up old collection immediately after successful migration
        try:
            client.delete_collection(source_collection_full)
            logger.info(f"Deleted old collection: {source_collection_full}")
        except Exception as e:
            # Non-fatal error - log and continue
            logger.warning(f"Failed to delete old collection {source_collection_full}: {str(e)}")

    return migrated_count


async def migrate_index_to_unified(
    index: models.DatasetIndex,
    dataset: models.Dataset,
    unified_collection_name: str,
    extractor_info: Union[models.Extractor, Tuple[models.Timeline, models.Extractor]],
    vectorstore: Any,
    vector_dimension: int,
    dry_run: bool = True
) -> int:
    """
    Migrate a single index to unified collection with enhanced metadata.

    Args:
        index: DatasetIndex to migrate
        dataset: Associated Dataset
        unified_collection_name: Target unified collection name (includes vector dimension)
        extractor_info: Extractor or (Timeline, Extractor) tuple
        vectorstore: Vectorstore instance for migration
        vector_dimension: Expected vector dimension for validation
        dry_run: If True, only count documents without migrating

    Returns:
        Number of documents migrated
    """
    logger.info(f"Migrating index {index.name} from dataset {dataset.name}")

    # Validate source collection and count documents
    source_collection_name, source_collection_full, document_count = await validate_source_collection_and_count_documents(
        index, dataset, vectorstore
    )

    if document_count == 0:
        return 0

    # Validate vector dimension compatibility
    detected_dimension = await detect_vector_dimension_from_index(index, dataset, vectorstore)
    if detected_dimension != vector_dimension:
        logger.warning(f"Vector dimension mismatch for index {index.name}: expected {vector_dimension}, detected {detected_dimension}")
        # Update the expected dimension to match detected (for compatibility)
        vector_dimension = detected_dimension

    if dry_run:
        return document_count

    # Migrate documents in batches
    migrated_count = 0
    offset = None
    batch_size = 1000
    client = vectorstore.client
    unified_collection_full = ira_vectorstore.get_index_namespace(unified_collection_name)

    while True:
        # Retrieve batch of documents
        points, next_offset = client.scroll(
            collection_name=source_collection_full,
            limit=batch_size,
            with_payload=True,
            with_vectors=True,
            offset=offset
        )

        if not points:
            break

        # Process batch with metadata enhancement
        enhanced_points = await process_document_batch_with_metadata_enhancement(
            points, extractor_info, dataset, vector_dimension
        )

        # Upload documents and handle cleanup if this is the final batch
        is_final_batch = (migrated_count + len(enhanced_points)) >= document_count
        migrated_count = await upload_documents_and_cleanup(
            client, source_collection_full, unified_collection_full, enhanced_points,
            migrated_count, document_count, cleanup_after_migration=is_final_batch
        )

        offset = next_offset
        if not offset:
            break

    logger.info(f"Successfully migrated {migrated_count} documents from {source_collection_full}")
    return migrated_count


async def update_indexes_for_unified_collections(
    index_dimension_map: Dict[str, int]
):
    """
    Update DatasetIndex configurations to use unified collections with vector dimensions.

    Args:
        index_dimension_map: Dict mapping index_id -> vector_dimension
    """
    logger.info(f"Updating {len(index_dimension_map)} indexes for unified collections")

    for index_id, vector_dimension in index_dimension_map.items():
        index = await db_api.get_dataset_index(index_id)
        current_config = index.get_config()

        # Update config to use unified collection with vector dimension
        updated_config = current_config.copy()
        updated_config.update({
            'use_unified_collection': True,
            'collection_type': 'UNIFIED_WORKSPACE',
            'vector_dimension': vector_dimension,
            'migrated_at': datetime.datetime.now(datetime.UTC).isoformat()
        })

        await db_api.update_dataset_index(index, {'config': updated_config})
        logger.info(f"Updated index {index_id} configuration for unified collection (dimension: {vector_dimension})")


# Helper functions
async def get_datasets_by_ids(dataset_ids: List[str]) -> List[models.Dataset]:
    """Get datasets by their IDs."""
    datasets = []
    for dataset_id in dataset_ids:
        dataset = await db_api.get_dataset(dataset_id)
        datasets.append(dataset)
    return datasets


def parse_args():
    parser = argparse.ArgumentParser(description='Migrate extractors/timelines to unified collections')
    parser.add_argument('--workspace-id', type=int, help='Specific workspace ID to migrate (optional)')
    parser.add_argument('--dry-run', action='store_true', help='Perform dry run without making changes')

    return parser.parse_args()


async def main():
    args = parse_args()
    utils.setup_logging()

    async with base.session_context():
        if args.workspace_id:
            # Migrate specific workspace
            logger.info(f"Migrating specific workspace: {args.workspace_id}")
            workspace = await db_api.get_workspace_by_id(args.workspace_id)
            result = await migrate_workspace_to_unified_collections(workspace, dry_run=args.dry_run)
            logger.info(f"Migration result: {result}")
        else:
            # Migrate all workspaces
            logger.info("Migrating all workspaces")
            result = await migrate_all_workspaces(dry_run=args.dry_run)
            logger.info(f"Migration summary: {result}")


if __name__ == '__main__':
    asyncio.run(main())
